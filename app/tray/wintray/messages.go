//go:build windows

package wintray

const (
	firstTimeTitle   = "Ollama is running"
	firstTimeMessage = "Click here to get started"
	updateTitle      = "Update available"
	updateMessage    = "Ollama version %s is ready to install"

	quitMenuTitle            = "Quit Ollama"
	updateAvailableMenuTitle = "An update is available"
	updateMenuTitle          = "Restart to update"
	diagLogsMenuTitle        = "View logs"
)
