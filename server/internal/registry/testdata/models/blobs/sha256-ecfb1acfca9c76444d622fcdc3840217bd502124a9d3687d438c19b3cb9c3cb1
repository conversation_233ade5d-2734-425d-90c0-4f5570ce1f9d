{"schemaVersion": 2, "mediaType": "application/vnd.docker.distribution.manifest.v2+json", "config": {"mediaType": "application/vnd.docker.container.image.v1+json", "digest": "sha256:ca239d7bd8ea90e4a5d2e6bf88f8d74a47b14336e73eb4e18bed4dd325018116", "size": 267}, "layers": [{"mediaType": "application/vnd.ollama.image.model", "digest": "sha256:a4e5e156ddec27e286f75328784d7106b60a4eb1d246e950a001a3f944fbda99", "size": 24}]}