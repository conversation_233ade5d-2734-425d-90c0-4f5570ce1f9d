name: latest

on:
  release:
    types: [released]

jobs:
  update-latest:
    environment: release
    runs-on: linux
    steps:
      - uses: actions/checkout@v4
      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ vars.DOCKER_USER }}
          password: ${{ secrets.DOCKER_ACCESS_TOKEN }}
      - name: Tag images as latest
        env:
          PUSH: "1"
        shell: bash
        run: |
          export "VERSION=${GITHUB_REF_NAME#v}"
          ./scripts/tag_latest.sh
