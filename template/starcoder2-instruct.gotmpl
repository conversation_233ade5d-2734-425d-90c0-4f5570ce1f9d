{{- $system := "" }}
{{- range .Messages }}
{{- if eq .Role "system" }}
{{- if not $system }}{{ $system = .Content }}
{{- else }}{{ $system = printf "%s\n\n%s" $system .Content }}
{{- end }}
{{- else if eq .Role "user" }}
{{- if $system }}{{ $system }}

{{ $system = "" }}
{{- end }}### Instruction
{{ .Content }}

{{ else if eq .Role "assistant" }}### Response
{{ .Content }}<|endoftext|>

{{ end }}
{{- end }}### Response
